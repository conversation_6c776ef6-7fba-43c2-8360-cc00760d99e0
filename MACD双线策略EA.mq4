//+------------------------------------------------------------------+
//|                                                MACD双线策略EA.mq4 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property strict

// 输入参数
extern int FastEMA = 12;           // 快线EMA周期
extern int SlowEMA = 26;           // 慢线EMA周期
extern int MACDEMA = 9;            // MACD信号线EMA周期
extern double LotSize = 0.1;       // 手数
extern int MagicNumber = 12345;    // 魔术数字
extern bool UseTrailingStop = true; // 是否使用移动止损
extern int TrailingStopPoints = 200; // 移动止损触发盈利点数
extern int MaxConsecutiveBars = 6; // 连续柱数a的最大值限制
extern int StopLossBuffer = 50;    // 止损缓冲点数
extern int InitialStopLossBuffer = 100; // 初始止损缓冲点数
extern bool UseMarginCall = false; // 是否使用亏损补仓功能
extern int MaxMarginOrders = 4;    // 最大补仓单数
extern int Slippage = 3;           // 滑点
extern bool UseMACDTrendFilter = false; // 是否使用MACD快线趋势验证
extern bool UseStopLossRecovery = false;  // 是否使用止损触发回本功能
extern bool UseRecoveryTakeProfit = true;    // 是否为反向回本单设置止盈
extern int RecoveryLookback = 3;    // 反向单止损计算的K线回溯数量
extern int RecoveryStopLossBuffer = 144;  // 反向单止损缓冲点数

// 全局变量
double FastEMABuffer[];
double SlowEMABuffer[];
double DIFBuffer[];
double DEABuffer[];
double MACDBuffer[];
bool NewBar = false;

// 补仓相关全局变量
struct MarginCallInfo
{
    int MainOrderTicket;        // 主订单号
    double OpenPrice;           // 开仓价
    double StopLoss;           // 止损价
    int OrderType;             // 订单类型
    bool MarginLevels[20];     // 补仓级别标记（最多支持20级）
    int MarginOrderTickets[20]; // 补仓订单号数组
};

MarginCallInfo BuyMarginInfo;   // 多单补仓信息
MarginCallInfo SellMarginInfo;  // 空单补仓信息

// 止损触发回本功能相关全局变量
struct StopLossRecoveryInfo
{
    int MainOrderTicket;        // 主订单号
    double OpenPrice;           // 开仓价
    double OriginalStopLoss;    // 原始止损价（首单开仓时的止损）
    int OrderType;              // 订单类型
    double LotSize;             // 初始持仓手数
    double TotalLotSize;        // 总持仓手数（包括补仓）
    bool IsActive;              // 是否激活监控
    bool IsRecoveryOrderPlaced; // 是否已下回本单
};

StopLossRecoveryInfo BuyRecoveryInfo;   // 多单回本信息
StopLossRecoveryInfo SellRecoveryInfo;  // 空单回本信息

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化数组
    ArraySetAsSeries(FastEMABuffer, true);
    ArraySetAsSeries(SlowEMABuffer, true);
    ArraySetAsSeries(DIFBuffer, true);
    ArraySetAsSeries(DEABuffer, true);
    ArraySetAsSeries(MACDBuffer, true);
    
    ArrayResize(FastEMABuffer, Bars);
    ArrayResize(SlowEMABuffer, Bars);
    ArrayResize(DIFBuffer, Bars);
    ArrayResize(DEABuffer, Bars);
    ArrayResize(MACDBuffer, Bars);
    
    // 计算历史MACD数据
    CalculateMACD();

    // 参数验证
    if(MaxMarginOrders > 20)
    {
        Print("错误：最大补仓单数不能超过20，当前设置：", MaxMarginOrders);
        MaxMarginOrders = 20;
        Print("已自动调整为：", MaxMarginOrders);
    }

    // 初始化补仓信息
    InitMarginCallInfo();

    // 初始化止损触发回本信息
    InitStopLossRecoveryInfo();

    Print("MACD双线策略EA初始化完成");
    Print("MACD快线趋势验证功能：", UseMACDTrendFilter ? "已启用" : "已禁用");
    Print("止损触发回本功能：", UseStopLossRecovery ? "已启用" : "已禁用");
    if(UseStopLossRecovery)
    {
        Print("止损触发回本单止盈功能：", UseRecoveryTakeProfit ? "已启用" : "已禁用");
        Print("回本单止损计算K线回溯数量：", RecoveryLookback);
    }
    if(UseMACDTrendFilter)
    {
        Print("多单条件：MACD柱从负转正 + MACD快线值>0");
        Print("空单条件：MACD柱从正转负 + MACD快线值<0");
    }
    else
    {
        Print("多单条件：MACD柱从负转正");
        Print("空单条件：MACD柱从正转负");
    }
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("MACD双线策略EA已停止");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查是否有新K线
    static datetime LastBarTime = 0;
    if(Time[0] != LastBarTime)
    {
        LastBarTime = Time[0];
        NewBar = true;
        
        // 重新计算MACD
        CalculateMACD();
        
        // 检查交易信号
        CheckTradingSignals();
        
        // 移动止损
        if(UseTrailingStop)
            TrailingStop();

        // 补仓检查
        if(UseMarginCall)
            CheckMarginCall();
    }
    else
    {
        NewBar = false;
    }
    
    // 检查止损触发情况（每个tick都检查）
    if(UseStopLossRecovery)
    {
        CheckStopLossTriggered();
    }
}

//+------------------------------------------------------------------+
//| 计算MACD指标                                                      |
//+------------------------------------------------------------------+
void CalculateMACD()
{
    int limit = MathMin(Bars - 1, 1000); // 限制计算范围以提高性能
    
    // 计算快线和慢线EMA
    for(int i = limit; i >= 0; i--)
    {
        if(i == limit)
        {
            FastEMABuffer[i] = Close[i];
            SlowEMABuffer[i] = Close[i];
        }
        else
        {
            double fastAlpha = 2.0 / (FastEMA + 1);
            double slowAlpha = 2.0 / (SlowEMA + 1);
            
            FastEMABuffer[i] = fastAlpha * Close[i] + (1 - fastAlpha) * FastEMABuffer[i + 1];
            SlowEMABuffer[i] = slowAlpha * Close[i] + (1 - slowAlpha) * SlowEMABuffer[i + 1];
        }
        
        // 计算DIF
        DIFBuffer[i] = FastEMABuffer[i] - SlowEMABuffer[i];
    }
    
    // 计算DEA (信号线)
    for(int i = limit; i >= 0; i--)
    {
        if(i == limit)
        {
            DEABuffer[i] = DIFBuffer[i];
        }
        else
        {
            double w = 2.0 / (MACDEMA + 1);
            double w1 = 1.0 - w;
            DEABuffer[i] = w * DIFBuffer[i] + w1 * DEABuffer[i + 1];
        }
        
        // 计算MACD柱
        MACDBuffer[i] = (DIFBuffer[i] - DEABuffer[i]) * 2;
    }
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    if(!NewBar) return;
    
    // 确保有足够的数据
    if(Bars < 100) return;
    
    // 检查多单信号
    if(MACDBuffer[1] > 0 && MACDBuffer[2] < 0)
    {
        // MACD快线趋势验证（如果启用）
        bool trendFilterPassed = true;
        if(UseMACDTrendFilter)
        {
            // 多单需要MACD快线值（DIF）大于0
            if(DIFBuffer[1] <= 0)
            {
                trendFilterPassed = false;
            }
        }

        if(trendFilterPassed)
        {
            // 统计从上上柱开始向前连续小于0的MACD柱值数a
            int a = CountConsecutiveNegative(2);
            if(a > 0 && a < MaxConsecutiveBars) // 添加a值限制条件
            {
                int b = 6 + 2 * a;
                int startBar = b + 2 + a;
                int endBar = 2 + a;

                // 检查指定范围内的MACD柱值是否都大于0
                if(CheckAllPositive(startBar, endBar))
                {
                    // 计算止损价格（最低点减去缓冲点数）
                    double stopLoss = GetLowestPrice(1, endBar) - InitialStopLossBuffer * Point;
                    OpenBuyOrder(stopLoss);
                }
            }
        }
    }
    
    // 检查空单信号
    if(MACDBuffer[1] < 0 && MACDBuffer[2] > 0)
    {
        // MACD快线趋势验证（如果启用）
        bool trendFilterPassed = true;
        if(UseMACDTrendFilter)
        {
            // 空单需要MACD快线值（DIF）小于0
            if(DIFBuffer[1] >= 0)
            {
                trendFilterPassed = false;
            }
        }

        if(trendFilterPassed)
        {
            // 统计从上上柱开始向前连续大于0的MACD柱值数a
            int a = CountConsecutivePositive(2);
            if(a > 0 && a < MaxConsecutiveBars) // 添加a值限制条件
            {
                int b = 6 + 2 * a;
                int startBar = b + 2 + a;
                int endBar = 2 + a;

                // 检查指定范围内的MACD柱值是否都小于0
                if(CheckAllNegative(startBar, endBar))
                {
                    // 计算止损价格（最高点加上缓冲点数）
                    double stopLoss = GetHighestPrice(1, endBar) + InitialStopLossBuffer * Point;
                    OpenSellOrder(stopLoss);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 统计连续负值数量                                                  |
//+------------------------------------------------------------------+
int CountConsecutiveNegative(int startBar)
{
    int count = 0;
    for(int i = startBar; i < Bars && MACDBuffer[i] < 0; i++)
    {
        count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| 统计连续正值数量                                                  |
//+------------------------------------------------------------------+
int CountConsecutivePositive(int startBar)
{
    int count = 0;
    for(int i = startBar; i < Bars && MACDBuffer[i] > 0; i++)
    {
        count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| 检查指定范围内是否都为正值                                        |
//+------------------------------------------------------------------+
bool CheckAllPositive(int startBar, int endBar)
{
    if(startBar >= Bars || endBar < 0) return false;
    
    for(int i = startBar; i >= endBar; i--)
    {
        if(MACDBuffer[i] <= 0) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 检查指定范围内是否都为负值                                        |
//+------------------------------------------------------------------+
bool CheckAllNegative(int startBar, int endBar)
{
    if(startBar >= Bars || endBar < 0) return false;
    
    for(int i = startBar; i >= endBar; i--)
    {
        if(MACDBuffer[i] >= 0) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最低价                                            |
//+------------------------------------------------------------------+
double GetLowestPrice(int startBar, int endBar)
{
    double lowest = Low[startBar];
    for(int i = startBar; i <= endBar && i < Bars; i++)
    {
        if(Low[i] < lowest) lowest = Low[i];
    }
    return lowest;
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最高价                                            |
//+------------------------------------------------------------------+
double GetHighestPrice(int startBar, int endBar)
{
    double highest = High[startBar];
    for(int i = startBar; i <= endBar && i < Bars; i++)
    {
        if(High[i] > highest) highest = High[i];
    }
    return highest;
}

//+------------------------------------------------------------------+
//| 开多单                                                           |
//+------------------------------------------------------------------+
void OpenBuyOrder(double stopLoss)
{
    // 检查是否已有同方向订单
    if(HasOpenOrder(OP_BUY)) return;

    double price = Ask;
    double sl = stopLoss;
    double tp = 0; // 不设置止盈

    int ticket = OrderSend(Symbol(), OP_BUY, LotSize, price, Slippage, sl, tp,
                          "MACD双线策略-多单", MagicNumber, 0, clrBlue);

    if(ticket > 0)
    {
        string trendInfo = "";
        if(UseMACDTrendFilter)
        {
            trendInfo = "，MACD快线值：" + DoubleToString(DIFBuffer[1], 5);
        }
        Print("多单开仓成功，订单号：", ticket, "，价格：", price, "，止损：", sl, trendInfo);

        // 记录止损触发回本信息
        if(UseStopLossRecovery)
        {
            BuyRecoveryInfo.MainOrderTicket = ticket;
            BuyRecoveryInfo.OpenPrice = price;
            BuyRecoveryInfo.OriginalStopLoss = sl;
            BuyRecoveryInfo.OrderType = OP_BUY;
            BuyRecoveryInfo.LotSize = LotSize; // 初始手数，后续会动态计算总持仓
            BuyRecoveryInfo.TotalLotSize = LotSize; // 初始总手数
            BuyRecoveryInfo.IsActive = true;
            BuyRecoveryInfo.IsRecoveryOrderPlaced = false;
        }

        // 记录补仓信息
        if(UseMarginCall)
        {
            BuyMarginInfo.MainOrderTicket = ticket;
            BuyMarginInfo.OpenPrice = price;
            BuyMarginInfo.StopLoss = sl;
            BuyMarginInfo.OrderType = OP_BUY;
            ResetMarginLevels(BuyMarginInfo);
        }
    }
    else
    {
        Print("多单开仓失败，错误代码：", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 开空单                                                           |
//+------------------------------------------------------------------+
void OpenSellOrder(double stopLoss)
{
    // 检查是否已有同方向订单
    if(HasOpenOrder(OP_SELL)) return;

    double price = Bid;
    double sl = stopLoss;
    double tp = 0; // 不设置止盈

    int ticket = OrderSend(Symbol(), OP_SELL, LotSize, price, Slippage, sl, tp,
                          "MACD双线策略-空单", MagicNumber, 0, clrRed);

    if(ticket > 0)
    {
        string trendInfo = "";
        if(UseMACDTrendFilter)
        {
            trendInfo = "，MACD快线值：" + DoubleToString(DIFBuffer[1], 5);
        }
        Print("空单开仓成功，订单号：", ticket, "，价格：", price, "，止损：", sl, trendInfo);

        // 记录止损触发回本信息
        if(UseStopLossRecovery)
        {
            SellRecoveryInfo.MainOrderTicket = ticket;
            SellRecoveryInfo.OpenPrice = price;
            SellRecoveryInfo.OriginalStopLoss = sl;
            SellRecoveryInfo.OrderType = OP_SELL;
            SellRecoveryInfo.LotSize = LotSize; // 初始手数，后续会动态计算总持仓
            SellRecoveryInfo.TotalLotSize = LotSize; // 初始总手数
            SellRecoveryInfo.IsActive = true;
            SellRecoveryInfo.IsRecoveryOrderPlaced = false;
        }

        // 记录补仓信息
        if(UseMarginCall)
        {
            SellMarginInfo.MainOrderTicket = ticket;
            SellMarginInfo.OpenPrice = price;
            SellMarginInfo.StopLoss = sl;
            SellMarginInfo.OrderType = OP_SELL;
            ResetMarginLevels(SellMarginInfo);
        }
    }
    else
    {
        Print("空单开仓失败，错误代码：", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 检查是否有指定类型的开仓订单                                      |
//+------------------------------------------------------------------+
bool HasOpenOrder(int orderType)
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber && OrderType() == orderType)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 计算指定类型订单的总手数                                          |
//+------------------------------------------------------------------+
double CalculateTotalLots(int orderType)
{
    double totalLots = 0;
    
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber && OrderType() == orderType)
            {
                totalLots += OrderLots();
            }
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| 移动止损                                                         |
//+------------------------------------------------------------------+
void TrailingStop()
{
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
            {
                if(OrderType() == OP_BUY)
                {
                    // 多单：检测上一柱最低点是否盈利200点（可调）
                    double baseStopLoss = Low[1] - StopLossBuffer * Point; // 最低点减去缓冲点数
                    double currentProfit = (baseStopLoss - OrderOpenPrice()) / Point;

                    // 检查是否达到盈利点数要求且新止损更优
                    if(currentProfit >= TrailingStopPoints && (OrderStopLoss() == 0 || baseStopLoss > OrderStopLoss()))
                    {
                        bool result = OrderModify(OrderTicket(), OrderOpenPrice(), baseStopLoss, OrderTakeProfit(), 0, clrBlue);
                        if(result)
                        {
                            Print("多单移动止损成功，订单号：", OrderTicket(), "，新止损：", baseStopLoss, "，当前盈利：", currentProfit, "点");
                        }
                        else
                        {
                            Print("多单移动止损失败，订单号：", OrderTicket(), "，错误代码：", GetLastError());
                        }
                    }
                }
                else if(OrderType() == OP_SELL)
                {
                    // 空单：检测上一柱最高点是否盈利200点（可调）
                    double baseStopLoss = High[1] + StopLossBuffer * Point; // 最高点加上缓冲点数
                    double currentProfit = (OrderOpenPrice() - baseStopLoss) / Point;

                    // 检查是否达到盈利点数要求且新止损更优
                    if(currentProfit >= TrailingStopPoints && (OrderStopLoss() == 0 || baseStopLoss < OrderStopLoss()))
                    {
                        bool result = OrderModify(OrderTicket(), OrderOpenPrice(), baseStopLoss, OrderTakeProfit(), 0, clrRed);
                        if(result)
                        {
                            Print("空单移动止损成功，订单号：", OrderTicket(), "，新止损：", baseStopLoss, "，当前盈利：", currentProfit, "点");
                        }
                        else
                        {
                            Print("空单移动止损失败，订单号：", OrderTicket(), "，错误代码：", GetLastError());
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化补仓信息                                                    |
//+------------------------------------------------------------------+
void InitMarginCallInfo()
{
    BuyMarginInfo.MainOrderTicket = 0;
    BuyMarginInfo.OpenPrice = 0;
    BuyMarginInfo.StopLoss = 0;
    BuyMarginInfo.OrderType = -1;

    SellMarginInfo.MainOrderTicket = 0;
    SellMarginInfo.OpenPrice = 0;
    SellMarginInfo.StopLoss = 0;
    SellMarginInfo.OrderType = -1;

    ResetMarginLevels(BuyMarginInfo);
    ResetMarginLevels(SellMarginInfo);
}

//+------------------------------------------------------------------+
//| 重置补仓级别标记                                                  |
//+------------------------------------------------------------------+
void ResetMarginLevels(MarginCallInfo &info)
{
    for(int i = 0; i < 20; i++)
    {
        info.MarginLevels[i] = false;
        info.MarginOrderTickets[i] = 0;
    }
}

//+------------------------------------------------------------------+
//| 检查补仓条件                                                      |
//+------------------------------------------------------------------+
void CheckMarginCall()
{
    // 检查多单补仓
    if(BuyMarginInfo.MainOrderTicket > 0 && OrderSelect(BuyMarginInfo.MainOrderTicket, SELECT_BY_TICKET))
    {
        if(OrderCloseTime() == 0) // 订单仍然开放
        {
            CheckBuyMarginCall();
        }
        else
        {
            // 主订单已关闭，重置补仓信息
            ResetMarginCallInfo(BuyMarginInfo);
        }
    }

    // 检查空单补仓
    if(SellMarginInfo.MainOrderTicket > 0 && OrderSelect(SellMarginInfo.MainOrderTicket, SELECT_BY_TICKET))
    {
        if(OrderCloseTime() == 0) // 订单仍然开放
        {
            CheckSellMarginCall();
        }
        else
        {
            // 主订单已关闭，重置补仓信息
            ResetMarginCallInfo(SellMarginInfo);
        }
    }
}

//+------------------------------------------------------------------+
//| 重置补仓信息                                                      |
//+------------------------------------------------------------------+
void ResetMarginCallInfo(MarginCallInfo &info)
{
    info.MainOrderTicket = 0;
    info.OpenPrice = 0;
    info.StopLoss = 0;
    info.OrderType = -1;
    ResetMarginLevels(info);
}

//+------------------------------------------------------------------+
//| 检查多单补仓                                                      |
//+------------------------------------------------------------------+
void CheckBuyMarginCall()
{
    double currentPrice = Bid;
    double openPrice = BuyMarginInfo.OpenPrice;
    double stopLoss = BuyMarginInfo.StopLoss;

    // 计算开仓价和止损价之间的距离
    double totalDistance = openPrice - stopLoss;
    if(totalDistance <= 0) return; // 无效距离

    // 计算每一份的距离
    double stepDistance = totalDistance / (MaxMarginOrders + 1);

    // 检查每个补仓级别
    for(int level = 1; level <= MaxMarginOrders && level <= 20; level++) // 添加数组边界检查
    {
        if(!BuyMarginInfo.MarginLevels[level - 1]) // 该级别还未补仓
        {
            double marginPrice = openPrice - stepDistance * level;

            // 检查当前价格是否到达补仓点
            if(currentPrice <= marginPrice)
            {
                // 执行补仓
                int ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, Slippage, stopLoss, 0,
                                     "补仓-" + IntegerToString(level), MagicNumber, 0, clrBlue);

                if(ticket > 0)
                {
                    BuyMarginInfo.MarginLevels[level - 1] = true;
                    BuyMarginInfo.MarginOrderTickets[level - 1] = ticket;
                    Print("多单补仓成功，级别：", level, "，订单号：", ticket, "，价格：", Ask);

                    // 更新止损触发回本功能的总手数
                    if(UseStopLossRecovery && BuyRecoveryInfo.IsActive)
                    {
                        BuyRecoveryInfo.TotalLotSize += LotSize;
                        Print("更新多单总持仓手数：", BuyRecoveryInfo.TotalLotSize);
                    }
                }
                else
                {
                    Print("多单补仓失败，级别：", level, "，错误代码：", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查空单补仓                                                      |
//+------------------------------------------------------------------+
void CheckSellMarginCall()
{
    double currentPrice = Ask;
    double openPrice = SellMarginInfo.OpenPrice;
    double stopLoss = SellMarginInfo.StopLoss;

    // 计算开仓价和止损价之间的距离
    double totalDistance = stopLoss - openPrice;
    if(totalDistance <= 0) return; // 无效距离

    // 计算每一份的距离
    double stepDistance = totalDistance / (MaxMarginOrders + 1);

    // 检查每个补仓级别
    for(int level = 1; level <= MaxMarginOrders && level <= 20; level++) // 添加数组边界检查
    {
        if(!SellMarginInfo.MarginLevels[level - 1]) // 该级别还未补仓
        {
            double marginPrice = openPrice + stepDistance * level;

            // 检查当前价格是否到达补仓点
            if(currentPrice >= marginPrice)
            {
                // 执行补仓
                int ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, Slippage, stopLoss, 0,
                                     "补仓-" + IntegerToString(level), MagicNumber, 0, clrRed);

                if(ticket > 0)
                {
                    SellMarginInfo.MarginLevels[level - 1] = true;
                    SellMarginInfo.MarginOrderTickets[level - 1] = ticket;
                    Print("空单补仓成功，级别：", level, "，订单号：", ticket, "，价格：", Bid);

                    // 更新止损触发回本功能的总手数
                    if(UseStopLossRecovery && SellRecoveryInfo.IsActive)
                    {
                        SellRecoveryInfo.TotalLotSize += LotSize;
                        Print("更新空单总持仓手数：", SellRecoveryInfo.TotalLotSize);
                    }
                }
                else
                {
                    Print("空单补仓失败，级别：", level, "，错误代码：", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化止损触发回本信息                                            |
//+------------------------------------------------------------------+
void InitStopLossRecoveryInfo()
{
    BuyRecoveryInfo.MainOrderTicket = 0;
    BuyRecoveryInfo.OpenPrice = 0;
    BuyRecoveryInfo.OriginalStopLoss = 0;
    BuyRecoveryInfo.OrderType = -1;
    BuyRecoveryInfo.LotSize = 0;
    BuyRecoveryInfo.TotalLotSize = 0;
    BuyRecoveryInfo.IsActive = false;
    BuyRecoveryInfo.IsRecoveryOrderPlaced = false;

    SellRecoveryInfo.MainOrderTicket = 0;
    SellRecoveryInfo.OpenPrice = 0;
    SellRecoveryInfo.OriginalStopLoss = 0;
    SellRecoveryInfo.OrderType = -1;
    SellRecoveryInfo.LotSize = 0;
    SellRecoveryInfo.TotalLotSize = 0;
    SellRecoveryInfo.IsActive = false;
    SellRecoveryInfo.IsRecoveryOrderPlaced = false;
}

//+------------------------------------------------------------------+
//| 检查止损是否触发                                                  |
//+------------------------------------------------------------------+
void CheckStopLossTriggered()
{
    // 检查多单止损触发
    if(BuyRecoveryInfo.IsActive && !BuyRecoveryInfo.IsRecoveryOrderPlaced)
    {
        // 检查主订单是否还存在
        if(OrderSelect(BuyRecoveryInfo.MainOrderTicket, SELECT_BY_TICKET))
        {
            if(OrderCloseTime() != 0) // 订单已关闭
            {
                // 检查是否是因为止损触发关闭的
                double currentPrice = Bid;
                if(currentPrice <= BuyRecoveryInfo.OriginalStopLoss + 5 * Point) // 允许5点滑点
                {
                    // 使用记录的总持仓手数（包括补仓后的总手数）
                    double totalLots = BuyRecoveryInfo.TotalLotSize;

                    // 止损触发，开设反向空单
                    OpenRecoveryOrder(OP_SELL, totalLots, BuyRecoveryInfo.OpenPrice, BuyRecoveryInfo.OriginalStopLoss);
                    BuyRecoveryInfo.IsRecoveryOrderPlaced = true;
                }
                else
                {
                    // 不是止损触发的关闭，重置监控
                    BuyRecoveryInfo.IsActive = false;
                }
            }
        }
        else
        {
            // 订单不存在，重置监控
            BuyRecoveryInfo.IsActive = false;
        }
    }

    // 检查空单止损触发
    if(SellRecoveryInfo.IsActive && !SellRecoveryInfo.IsRecoveryOrderPlaced)
    {
        // 检查主订单是否还存在
        if(OrderSelect(SellRecoveryInfo.MainOrderTicket, SELECT_BY_TICKET))
        {
            if(OrderCloseTime() != 0) // 订单已关闭
            {
                // 检查是否是因为止损触发关闭的
                double currentPrice = Ask;
                if(currentPrice >= SellRecoveryInfo.OriginalStopLoss - 5 * Point) // 允许5点滑点
                {
                    // 使用记录的总持仓手数（包括补仓后的总手数）
                    double totalLots = SellRecoveryInfo.TotalLotSize;

                    // 止损触发，开设反向多单
                    OpenRecoveryOrder(OP_BUY, totalLots, SellRecoveryInfo.OpenPrice, SellRecoveryInfo.OriginalStopLoss);
                    SellRecoveryInfo.IsRecoveryOrderPlaced = true;
                }
                else
                {
                    // 不是止损触发的关闭，重置监控
                    SellRecoveryInfo.IsActive = false;
                }
            }
        }
        else
        {
            // 订单不存在，重置监控
            SellRecoveryInfo.IsActive = false;
        }
    }
}

//+------------------------------------------------------------------+
//| 开设反向回本单                                                    |
//+------------------------------------------------------------------+
void OpenRecoveryOrder(int orderType, double lotSize, double originalPrice, double stopLossPrice)
{
    double price, sl, tp;
    string orderComment;
    color orderColor;

    // 计算亏损点数（注意：这里不使用Point进行除法，直接计算价格差）
    double lossPoints;

    if(orderType == OP_BUY)
    {
        price = Ask;
        
        // 计算前三柱最低点作为止损，并减去缓冲点数
        sl = GetLowestPrice(1, RecoveryLookback) - RecoveryStopLossBuffer * Point;
        
        // 计算亏损点数（空单触发止损的亏损）
        lossPoints = MathAbs(stopLossPrice - originalPrice);
        
        // 设置止盈：当前价格 + 首单亏损点数
        if(UseRecoveryTakeProfit)
        {
            tp = price + lossPoints;
        }
        else
        {
            tp = 0;
        }
        
        orderComment = "止损回本-多单";
        orderColor = clrBlue;
    }
    else // OP_SELL
    {
        price = Bid;
        
        // 计算前三柱最高点作为止损，并加上缓冲点数
        sl = GetHighestPrice(1, RecoveryLookback) + RecoveryStopLossBuffer * Point;
        
        // 计算亏损点数（多单触发止损的亏损）
        lossPoints = MathAbs(originalPrice - stopLossPrice);
        
        // 设置止盈：当前价格 - 首单亏损点数
        if(UseRecoveryTakeProfit)
        {
            tp = price - lossPoints;
        }
        else
        {
            tp = 0;
        }
        
        orderComment = "止损回本-空单";
        orderColor = clrRed;
    }
    
    int ticket = OrderSend(Symbol(), orderType, lotSize, price, Slippage, sl, tp,
                         orderComment, MagicNumber, 0, orderColor);
                         
    if(ticket > 0)
    {
        Print("止损触发回本单开仓成功，订单号：", ticket, 
             "，类型：", orderType == OP_BUY ? "多单" : "空单",
             "，价格：", price, 
             "，止损：", sl,
             "，止盈：", tp != 0 ? DoubleToString(tp, Digits) : "无",
             "，手数：", lotSize,
             "，首单亏损点数：", DoubleToString(lossPoints, Digits));
    }
    else
    {
        Print("止损触发回本单开仓失败，错误代码：", GetLastError());
    }
}
